import React, { useState } from 'react';
import VoiceBot from './VoiceBot';
import VisionBot from './VisionBot';

const SasthraTools = () => {
  const [activeBot, setActiveBot] = useState(null);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex flex-col items-center p-4">
      {activeBot ? (
        <div className="w-full h-full min-h-screen">
          {activeBot === 'voice' ? (
            <VoiceBot onClose={() => setActiveBot(null)} />
          ) : (
            <VisionBot onClose={() => setActiveBot(null)} />
          )}
        </div>
      ) : (
        <div className="w-full max-w-6xl">
          <div className="text-center mb-12 mt-8">
            <div className="mb-6">
              <svg className="w-20 h-20 mx-auto text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
              </svg>
            </div>
            <h1 className="text-5xl font-bold text-white mb-4 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
              Sasthra Tools
            </h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Discover the power of AI with our advanced voice and vision technologies
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div
              className="group relative bg-gradient-to-br from-blue-50 to-cyan-100 rounded-2xl shadow-2xl p-8 cursor-pointer hover:shadow-3xl transform hover:scale-105 transition-all duration-300 overflow-hidden"
              onClick={() => setActiveBot('voice')}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 to-cyan-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="mb-6 relative z-10">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z" />
                  </svg>
                </div>
              </div>
              <div className="relative z-10">
                <h2 className="text-3xl font-bold text-gray-800 mb-3 group-hover:text-blue-700 transition-colors duration-300">
                  VoiceBot
                </h2>
                <p className="text-gray-600 text-lg mb-4 group-hover:text-gray-700 transition-colors duration-300">
                  Engage in natural conversations with our AI-powered voice assistant
                </p>
                <div className="flex items-center text-blue-600 font-semibold group-hover:text-blue-700 transition-colors duration-300">
                  <span>Start Voice Chat</span>
                  <svg
                    className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </div>
              </div>
              <div className="absolute top-4 right-4 w-24 h-24 bg-gradient-to-br from-blue-300/20 to-cyan-300/20 rounded-full blur-xl group-hover:scale-110 transition-transform duration-300"></div>
            </div>
            <div
              className="group relative bg-gradient-to-br from-purple-50 to-indigo-100 rounded-2xl shadow-2xl p-8 cursor-pointer hover:shadow-3xl transform hover:scale-105 transition-all duration-300 overflow-hidden"
              onClick={() => setActiveBot('vision')}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-purple-400/10 to-indigo-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="mb-6 relative z-10">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                  <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z" />
                  </svg>
                </div>
              </div>
              <div className="relative z-10">
                <h2 className="text-3xl font-bold text-gray-800 mb-3 group-hover:text-purple-700 transition-colors duration-300">
                  VisionBot
                </h2>
                <p className="text-gray-600 text-lg mb-4 group-hover:text-gray-700 transition-colors duration-300">
                  Experience the power of AI vision technology and image analysis
                </p>
                <div className="flex items-center text-purple-600 font-semibold group-hover:text-purple-700 transition-colors duration-300">
                  <span>Start Vision Analysis</span>
                  <svg
                    className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </div>
              </div>
              <div className="absolute top-4 right-4 w-24 h-24 bg-gradient-to-br from-purple-300/20 to-indigo-300/20 rounded-full blur-xl group-hover:scale-110 transition-transform duration-300"></div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SasthraTools;

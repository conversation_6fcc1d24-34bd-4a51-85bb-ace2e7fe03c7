// import React, { useEffect, useRef, useState } from 'react';
// import io from 'socket.io-client';
// import { X, BarChart3, Clock } from 'lucide-react';
// import EngagementDashboard from './EngagementDashboard';

// const FLASK_API_URL = 'https://sasthra.in';
// const SOCKETIO_URL = 'https://sasthra.in';
// const CAPTURE_INTERVAL_MS = 200;
// const DURATION_PER_OPTION_S = 5;
// const QUESTION_READING_TIME_S = 15;

// const ReadingTimer = ({ timeRemaining, isActive }) => {
//   if (!isActive) return null;

//   return (
//     <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-40">
//       <div className="bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 text-white px-12 py-8 rounded-3xl shadow-2xl animate-pulse border-4 border-white/30 backdrop-blur-sm">
//         <div className="text-center">
//           <div className="text-3xl font-bold mb-3 flex items-center justify-center gap-3">
//             <Clock className="animate-spin" size={32} />
//             Reading Time
//           </div>
//           <div className="text-6xl font-black animate-bounce drop-shadow-lg">{timeRemaining}s</div>
//           <div className="mt-3 text-lg opacity-90">Prepare for the question</div>
//         </div>
//       </div>
//     </div>
//   );
// };

// const CircularOptionDisplay = ({ option, timeRemaining, totalTime, isActive }) => {
//   const circumference = 2 * Math.PI * 120; // radius = 120 for larger circle
//   const progress = isActive ? (timeRemaining / totalTime) * circumference : 0;
//   const strokeDasharray = circumference;
//   const strokeDashoffset = circumference - progress;

//   // Color animation based on time remaining
//   const getTimerColor = () => {
//     const percentage = timeRemaining / totalTime;
//     if (percentage > 0.6) return '#10B981'; // Green
//     if (percentage > 0.3) return '#F59E0B'; // Yellow
//     return '#EF4444'; // Red
//   };

//   // Scale animation based on time
//   const getScale = () => {
//     const percentage = timeRemaining / totalTime;
//     return 1 + (1 - percentage) * 0.2; // Grows as time decreases
//   };

//   if (!isActive) return null;

//   return (
//     <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
//       {/* Animated background with multiple layers */}
//       <div className="absolute inset-0 bg-gradient-to-br from-blue-50/80 via-purple-50/80 to-pink-50/80 backdrop-blur-sm animate-pulse" />

//       {/* Floating background elements */}
//       <div className="absolute inset-0">
//         {[...Array(12)].map((_, i) => (
//           <div
//             key={i}
//             className="absolute w-4 h-4 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 animate-float"
//             style={{
//               top: `${10 + i * 7}%`,
//               left: `${5 + i * 8}%`,
//               animationDelay: `${i * 0.3}s`,
//               animationDuration: `${3 + (i % 3)}s`
//             }}
//           />
//         ))}
//       </div>

//       {/* Main circular container with enhanced animations */}
//       <div
//         className="relative transition-all duration-300 ease-out"
//         style={{ transform: `scale(${getScale()})` }}
//       >
//         {/* Outer glow ring */}
//         <div className="absolute inset-0 w-80 h-80 rounded-full bg-gradient-to-r from-blue-200 via-purple-200 to-pink-200 opacity-30 animate-spin-slow blur-xl" />

//         {/* Main circle container */}
//         <div className="relative w-64 h-64 flex items-center justify-center">
//           {/* Background circle with gradient */}
//           <svg
//             className="absolute inset-0 transform -rotate-90 w-full h-full drop-shadow-2xl"
//             viewBox="0 0 260 260"
//           >
//             {/* Background track */}
//             <circle
//               cx="130"
//               cy="130"
//               r="120"
//               fill="none"
//               stroke="rgba(255, 255, 255, 0.3)"
//               strokeWidth="12"
//               className="drop-shadow-lg"
//             />

//             {/* Animated progress circle */}
//             <circle
//               cx="130"
//               cy="130"
//               r="120"
//               fill="none"
//               stroke={getTimerColor()}
//               strokeWidth="12"
//               strokeLinecap="round"
//               strokeDasharray={strokeDasharray}
//               strokeDashoffset={strokeDashoffset}
//               className="transition-all duration-1000 ease-out drop-shadow-xl"
//               style={{
//                 filter: `drop-shadow(0 0 20px ${getTimerColor()}40)`
//               }}
//             />

//             {/* Inner decorative circles */}
//             <circle
//               cx="130"
//               cy="130"
//               r="100"
//               fill="none"
//               stroke="rgba(255, 255, 255, 0.2)"
//               strokeWidth="2"
//               strokeDasharray="5,5"
//               className="animate-spin-reverse"
//             />

//             {/* Gradient definitions */}
//             <defs>
//               <linearGradient id="optionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
//                 <stop offset="0%" stopColor="#3B82F6" />
//                 <stop offset="50%" stopColor="#8B5CF6" />
//                 <stop offset="100%" stopColor="#EC4899" />
//               </linearGradient>
//               <radialGradient id="centerGradient" cx="50%" cy="50%" r="50%">
//                 <stop offset="0%" stopColor="#FFFFFF" />
//                 <stop offset="100%" stopColor="#F8FAFC" />
//               </radialGradient>
//             </defs>
//           </svg>

//           {/* Center content with enhanced styling */}
//           <div className="relative z-10 flex flex-col items-center justify-center bg-gradient-to-br from-white via-blue-50 to-purple-50 rounded-full w-48 h-48 shadow-2xl border-4 border-white/50 backdrop-blur-sm">
//             {/* Option letter with enhanced animation */}
//             <div
//               className="text-8xl font-black bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-2 transform transition-all duration-500"
//               style={{
//                 transform: `scale(${1 + (1 - timeRemaining / totalTime) * 0.3}) rotate(${(totalTime - timeRemaining) * 2}deg)`,
//                 textShadow: '0 0 30px rgba(59, 130, 246, 0.3)'
//               }}
//             >
//               {option}
//             </div>

//             {/* Animated countdown number */}
//             <div
//               className="text-4xl font-bold bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent animate-pulse"
//               style={{
//                 transform: `scale(${1 + (1 - timeRemaining / totalTime) * 0.2})`,
//                 filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
//               }}
//             >
//               {timeRemaining}s
//             </div>

//             {/* Progress indicator dots */}
//             <div className="flex gap-1 mt-2">
//               {[...Array(totalTime)].map((_, i) => (
//                 <div
//                   key={i}
//                   className={`w-1.5 h-1.5 rounded-full transition-all duration-300 ${
//                     i < totalTime - timeRemaining
//                       ? 'bg-gradient-to-r from-red-400 to-orange-400 scale-125'
//                       : 'bg-gray-300 scale-100'
//                   }`}
//                 />
//               ))}
//             </div>
//           </div>

//           {/* Floating particles with enhanced animation */}
//           <div className="absolute inset-0 pointer-events-none">
//             {[...Array(8)].map((_, i) => (
//               <div
//                 key={i}
//                 className="absolute w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-bounce opacity-60"
//                 style={{
//                   top: `${15 + i * 10}%`,
//                   left: `${10 + i * 12}%`,
//                   animationDelay: `${i * 0.3}s`,
//                   animationDuration: `${2 + (i % 2)}s`
//                 }}
//               />
//             ))}
//           </div>

//           {/* Rotating outer ring */}
//           <div className="absolute inset-0 w-full h-full border-2 border-gradient-to-r from-blue-300 to-purple-300 rounded-full animate-spin opacity-20" />
//         </div>
//       </div>
//     </div>
//   );
// };

// const ContentQuiz = ({ processId, onClose }) => {
//   const [quizId, setQuizId] = useState(null);
//   const [questionData, setQuestionData] = useState(null);
//   const [quizStatus, setQuizStatus] = useState('');
//   const [summary, setSummary] = useState(null);
//   const [finalResults, setFinalResults] = useState(null);
//   const [liveFeedLogs, setLiveFeedLogs] = useState([]);
//   const [currentQuestionStartTime, setCurrentQuestionStartTime] = useState(null);
//   const [socketStatus, setSocketStatus] = useState('Disconnected');
//   const [error, setError] = useState(null);
//   const [centerCode] = useState(sessionStorage.getItem('centercode') || '');
//   const [currentOptionTimer, setCurrentOptionTimer] = useState(null);
//   const [currentOption, setCurrentOption] = useState('');
//   const [questionTimer, setQuestionTimer] = useState(null);
//   const [isReadingQuestion, setIsReadingQuestion] = useState(false);
//   const [showDashboard, setShowDashboard] = useState(false);
//   const [questionCount, setQuestionCount] = useState(0);
//   const [debugLogs, setDebugLogs] = useState([]);
//   const [socketEvents, setSocketEvents] = useState([]);
//   const [isCapturing, setIsCapturing] = useState(false);

//   const videoRef = useRef(null);
//   const canvasRef = useRef(null);
//   const socketRef = useRef(null);
//   const videoStreamRef = useRef(null);
//   const timerIntervalRef = useRef(null);
//   const optionTimerIntervalRef = useRef(null);

//   // Debug logging function
//   const addDebugLog = (message, data = null) => {
//     const timestamp = new Date().toISOString();
//     const logEntry = {
//       timestamp,
//       message,
//       data: data ? JSON.stringify(data) : null
//     };
//     console.log(`[DEBUG ${timestamp}] ${message}`, data);
//     setDebugLogs((prev) => [...prev.slice(-50), logEntry]); // Keep last 50 logs
//   };

//   // Socket event logging
//   const addSocketEvent = (eventName, data = null) => {
//     const timestamp = new Date().toISOString();
//     const eventEntry = {
//       timestamp,
//       event: eventName,
//       data: data ? JSON.stringify(data) : null
//     };
//     console.log(`[SOCKET ${timestamp}] ${eventName}`, data);
//     setSocketEvents((prev) => [...prev.slice(-50), eventEntry]); // Keep last 50 events
//   };

//   useEffect(() => {
//     if (quizId && videoRef.current) {
//       initCamera();
//     }
//   }, [quizId]);

//   useEffect(() => {
//     return () => {
//       addDebugLog('Component unmounting, cleaning up...');
//       stopCamera();
//       if (socketRef.current) {
//         socketRef.current.disconnect();
//       }
//       if (timerIntervalRef.current) {
//         clearInterval(timerIntervalRef.current);
//       }
//       if (optionTimerIntervalRef.current) {
//         clearInterval(optionTimerIntervalRef.current);
//       }
//     };
//   }, []);

//   const startQuiz = async () => {
//     addDebugLog('Starting quiz...', { processId, centerCode });

//     if (!processId || !centerCode) {
//       setError('Missing ProcessId or CenterCode');
//       return;
//     }

//     try {
//         const res = await fetch(`${FLASK_API_URL}/content/start_quiz`, {
//           method: 'POST',
//           headers: { 'Content-Type': 'application/json' },
//           body: JSON.stringify({ process_selector_id: processId, center_code: centerCode })
//         });

//       const data = await res.json();
//       addDebugLog('Quiz start response received', data);

//       if (!res.ok || data.error) throw new Error(data.error || `Server error ${res.status}`);

//       setQuestionData(data);
//       setQuizId(data.quiz_id);
//       setQuizStatus('Ready to start capture for this question.');
//       setQuestionCount(1);
//       addDebugLog('Quiz started successfully', { quizId: data.quiz_id });
//       setupSocketIO();
//     } catch (error) {
//       addDebugLog('Error starting quiz', error);
//       setError(`Error starting quiz: ${error.message}`);
//     }
//   };

//   const handleNextQuestion = async () => {
//     if (!quizId) return;

//     addDebugLog('Handling next question...');

//     try {
//       await startCaptureCycle();
//       setQuizStatus('Processing results and fetching next question...');

//       const res = await fetch(`${FLASK_API_URL}/content/next_question`, {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify({ quiz_id: quizId, process_selector_id: processId })
//       });

//       const data = await res.json();
//       addDebugLog('Next question response', data);

//       if (data.overall_summary) {
//         setFinalResults(data);
//         stopCamera();
//         socketRef.current?.disconnect();
//         setQuizStatus('Quiz completed! View engagement dashboard for detailed analytics.');
//         addDebugLog('Quiz completed', { questionCount });
//         if (questionCount >= 5) {
//           setShowDashboard(true);
//         }
//       } else if (data.question) {
//         setQuestionData(data);
//         setQuizStatus('Ready to start capture for this question.');
//         setQuestionCount((prev) => prev + 1);
//         addDebugLog('Next question loaded', { questionCount: questionCount + 1 });
//       }
//     } catch (error) {
//       addDebugLog('Error during quiz progression', error);
//       setError(`Error during quiz progression: ${error.message}`);
//       setQuizStatus(`Error: ${error.message}`);
//     }
//   };

//   const startCaptureCycle = async () => {
//     addDebugLog('Starting capture cycle...');
//     setLiveFeedLogs([]);
//     setIsCapturing(true);

//     // Question reading phase
//     addDebugLog('Starting question reading phase');
//     setIsReadingQuestion(true);
//     setQuestionTimer(QUESTION_READING_TIME_S);
//     setCurrentQuestionStartTime(Date.now());

//     timerIntervalRef.current = setInterval(() => {
//       setQuestionTimer((prev) => {
//         if (prev <= 1) {
//           clearInterval(timerIntervalRef.current);
//           setIsReadingQuestion(false);
//           addDebugLog('Question reading phase completed');
//           return 0;
//         }
//         return prev - 1;
//       });
//     }, 1000);

//     await new Promise((resolve) => setTimeout(resolve, QUESTION_READING_TIME_S * 1000));

//     setIsReadingQuestion(false);
//     setQuestionTimer(null);

//     // Options capture phase
//     addDebugLog('Starting options capture phase');
//     const options = ['a', 'b', 'c', 'd'];

//     for (let i = 0; i < options.length; i++) {
//       addDebugLog(`Processing option ${options[i]}`, { optionIndex: i });
//       setQuizStatus(
//         `Capturing for option ${options[i].toUpperCase()}... (${i + 1}/${options.length})`
//       );
//       await processSingleOption(options[i]);
//     }

//     setQuizStatus('Capture complete for this question. Results sent.');
//     addDebugLog('Capture cycle completed');

//     setCurrentOptionTimer(null);
//     setCurrentOption('');
//     setIsCapturing(false);
//   };

//   const processSingleOption = (optionChar) => {
//     addDebugLog(`Processing option ${optionChar}...`);

//     return new Promise((resolve) => {
//       const startTime = Date.now();
//       let frameCount = 0;

//       setCurrentOption(optionChar.toUpperCase());
//       setCurrentOptionTimer(DURATION_PER_OPTION_S);

//       // Start option timer
//       optionTimerIntervalRef.current = setInterval(() => {
//         const elapsed = Math.floor((Date.now() - startTime) / 1000);
//         const remaining = Math.max(0, DURATION_PER_OPTION_S - elapsed);
//         setCurrentOptionTimer(remaining);

//         if (remaining <= 0) {
//           clearInterval(optionTimerIntervalRef.current);
//         }
//       }, 1000);

//       // Start frame capture
//       const intervalId = setInterval(() => {
//         const responseTime = (Date.now() - startTime) / 1000;
//         captureAndSendFrame(optionChar, responseTime);
//         frameCount++;
//       }, CAPTURE_INTERVAL_MS);

//       // Stop after duration
//       setTimeout(() => {
//         clearInterval(intervalId);
//         if (optionTimerIntervalRef.current) {
//           clearInterval(optionTimerIntervalRef.current);
//         }
//         addDebugLog(`Option ${optionChar} processing completed`, { frameCount });
//         resolve();
//       }, DURATION_PER_OPTION_S * 1000);
//     });
//   };

//   const captureAndSendFrame = (optionChar, responseTime) => {
//     const video = videoRef.current;
//     const canvas = canvasRef.current;

//     if (!video || !canvas) {
//       addDebugLog('Video or canvas not available for frame capture');
//       return;
//     }

//     if (!socketRef.current?.connected) {
//       addDebugLog('Socket not connected, skipping frame capture');
//       return;
//     }

//     const ctx = canvas.getContext('2d');
//     canvas.width = video.videoWidth;
//     canvas.height = video.videoHeight;
//     ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
//     const frameData = canvas.toDataURL('image/jpeg', 0.6);

//     const framePayload = {
//       quiz_id: quizId,
//       frame: frameData,
//       option_char: optionChar,
//       response_time_seconds: responseTime
//     };

//     addDebugLog(`Sending frame for option ${optionChar}`, {
//       optionChar,
//       responseTime,
//       frameSize: frameData.length
//     });

//     socketRef.current.emit('process_frame', framePayload);
//   };

//   const setupSocketIO = () => {
//     addDebugLog('Setting up Socket.IO connection...');

//     if (socketRef.current) {
//       socketRef.current.disconnect();
//     }

//     socketRef.current = io(SOCKETIO_URL, {
//       path: '/socketio1/socket.io',
//       transports: ['websocket', 'polling'],
//       upgrade: true,
//       rememberUpgrade: true,
//       timeout: 20000,
//       forceNew: true,
//       autoConnect: true,
//       reconnection: true,
//       reconnectionAttempts: 5,
//       reconnectionDelay: 1000,
//       reconnectionDelayMax: 5000,
//       maxReconnectionAttempts: 5
//     });

//     socketRef.current.on('connect', () => {
//       setSocketStatus('Connected');
//       addDebugLog('Socket connected successfully');
//       addSocketEvent('connect');
//     });

//     socketRef.current.on('disconnect', (reason) => {
//       setSocketStatus('Disconnected');
//       addDebugLog('Socket disconnected', { reason });
//       addSocketEvent('disconnect', { reason });
//     });

//     socketRef.current.on('connect_error', (error) => {
//       setSocketStatus('Connection Error');
//       addDebugLog('Socket connection error', error);
//       addSocketEvent('connect_error', error);
//     });

//     socketRef.current.on('reconnect', (attemptNumber) => {
//       setSocketStatus('Reconnected');
//       addDebugLog('Socket reconnected', { attemptNumber });
//       addSocketEvent('reconnect', { attemptNumber });
//     });

//     socketRef.current.on('reconnect_error', (error) => {
//       addDebugLog('Socket reconnection error', error);
//       addSocketEvent('reconnect_error', error);
//     });

//     socketRef.current.on('reconnect_failed', () => {
//       setSocketStatus('Reconnection Failed');
//       addDebugLog('Socket reconnection failed');
//       addSocketEvent('reconnect_failed');
//     });

//     // Main hand raise event handler
//     socketRef.current.on('hand_raised', (data) => {
//       addDebugLog('Hand raised event received', data);
//       addSocketEvent('hand_raised', data);

//       const timestamp = new Date(data.detection_timestamp || Date.now());
//       const responseTime = currentQuestionStartTime
//         ? Math.round((timestamp.getTime() - currentQuestionStartTime) / 1000)
//         : 0;

//       const newLog = {
//         id: Date.now() + Math.random(), // Unique ID
//         student_name: data.student_name || 'Unknown Student',
//         option: data.option || currentOption || 'Unknown',
//         responseTime: responseTime,
//         timestamp: timestamp.toISOString(),
//         detection_timestamp: data.detection_timestamp,
//         confidence: data.confidence || 0,
//         raw_data: data
//       };

//       addDebugLog('Processing hand raise', newLog);

//       setLiveFeedLogs((prevLogs) => {
//         const updatedLogs = [...prevLogs, newLog];
//         addDebugLog('Live feed logs updated', { count: updatedLogs.length });
//         return updatedLogs;
//       });
//     });

//     // Listen for all possible hand detection events
//     const handEvents = [
//       'hand_raised',
//       'hand_detected',
//       'gesture_detected',
//       'student_response',
//       'frame_processed',
//       'detection_result'
//     ];

//     handEvents.forEach((eventName) => {
//       socketRef.current.on(eventName, (data) => {
//         addDebugLog(`Event received: ${eventName}`, data);
//         addSocketEvent(eventName, data);

//         // If it's a hand-related event, try to process it
//         if (
//           data &&
//           (data.hand_raised || data.gesture === 'hand_raised' || eventName.includes('hand'))
//         ) {
//           const timestamp = new Date(data.detection_timestamp || Date.now());
//           const responseTime = currentQuestionStartTime
//             ? Math.round((timestamp.getTime() - currentQuestionStartTime) / 1000)
//             : 0;

//           const newLog = {
//             id: Date.now() + Math.random(),
//             student_name: data.student_name || data.name || 'Student',
//             option: data.option || currentOption || 'Unknown',
//             responseTime: responseTime,
//             timestamp: timestamp.toISOString(),
//             detection_timestamp: data.detection_timestamp,
//             confidence: data.confidence || 0,
//             event_type: eventName,
//             raw_data: data
//           };

//           setLiveFeedLogs((prevLogs) => [...prevLogs, newLog]);
//         }
//       });
//     });

//     // Catch all events
//     socketRef.current.onAny((eventName, ...args) => {
//       addDebugLog(`Socket event: ${eventName}`, args);
//       addSocketEvent(eventName, args);
//     });

//     addDebugLog('Socket.IO setup completed');
//   };

//   const initCamera = async () => {
//     if (videoStreamRef.current) return;

//     addDebugLog('Initializing camera...');

//     try {
//       const stream = await navigator.mediaDevices.getUserMedia({
//         video: {
//           width: { ideal: 1280 },
//           height: { ideal: 720 }
//         }
//       });

//       if (videoRef.current) {
//         videoRef.current.srcObject = stream;
//         videoStreamRef.current = stream;
//         addDebugLog('Camera initialized successfully');
//       }
//     } catch (error) {
//       addDebugLog('Camera initialization error', error);
//       setError(`Camera error: ${error.message}`);
//     }
//   };

//   const stopCamera = () => {
//     addDebugLog('Stopping camera...');
//     if (videoStreamRef.current) {
//       videoStreamRef.current.getTracks().forEach((track) => track.stop());
//       videoStreamRef.current = null;
//     }
//     if (videoRef.current) {
//       videoRef.current.srcObject = null;
//     }
//   };

//   const testSocketConnection = () => {
//     if (socketRef.current) {
//       addDebugLog('Testing socket connection...');
//       socketRef.current.emit('test_connection', {
//         message: 'Hello from client',
//         timestamp: Date.now()
//       });
//     }
//   };

//   const clearDebugLogs = () => {
//     setDebugLogs([]);
//     setSocketEvents([]);
//     addDebugLog('Debug logs cleared');
//   };

//   const handleCloseDashboard = () => {
//     setShowDashboard(false);
//   };

//   useEffect(() => {
//     if (processId && centerCode) {
//       startQuiz();
//     }
//   }, [processId, centerCode]);

//   if (showDashboard && quizId) {
//     return <EngagementDashboard quizId={quizId} onClose={handleCloseDashboard} />;
//   }

//   return (
//     <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-8 max-w-7xl mx-auto">
//       <div className="flex justify-between items-center mb-6">
//         <h2 className="text-2xl font-bold text-gray-800">AI Tutor Quiz Tester</h2>
//         <button onClick={onClose} className="text-gray-600 hover:text-red-500 transition-colors">
//           <X size={24} />
//         </button>
//       </div>

//       <div
//         className={`p-4 rounded-lg mb-6 border ${
//           socketStatus === 'Connected'
//             ? 'bg-green-50 border-green-200 text-green-800'
//             : 'bg-red-50 border-red-200 text-red-800'
//         }`}
//       >
//         <div className="flex justify-between items-center">
//           <div>
//             <strong>Socket Status:</strong> {socketStatus}
//             <span className="ml-4 text-sm">Capturing: {isCapturing ? 'Yes' : 'No'}</span>
//           </div>
//           <div className="flex gap-2">
//             {socketRef.current && (
//               <button
//                 onClick={testSocketConnection}
//                 className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors text-sm"
//               >
//                 Test Connection
//               </button>
//             )}
//             <button
//               onClick={clearDebugLogs}
//               className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors text-sm"
//             >
//               Clear Logs
//             </button>
//           </div>
//         </div>
//       </div>

//       {!questionData && !error && (
//         <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
//           <h2 className="text-lg font-semibold mb-4 text-gray-800">Start a New Quiz</h2>
//           <div className="form-group mb-4">
//             <label htmlFor="process-selector-id" className="block mb-2 text-gray-700 font-medium">
//               Process Selector ID:
//             </label>
//             <input
//               type="text"
//               id="process-selector-id"
//               value={processId || ''}
//               readOnly
//               className="w-full p-3 border border-gray-300 rounded-lg bg-gray-100 text-gray-700"
//             />
//           </div>
//           <div className="form-group mb-4">
//             <label htmlFor="center-code" className="block mb-2 text-gray-700 font-medium">
//               Center Code:
//             </label>
//             <input
//               type="text"
//               id="center-code"
//               value={centerCode}
//               readOnly
//               className="w-full p-3 border border-gray-300 rounded-lg bg-gray-100 text-gray-700"
//             />
//           </div>
//           <button
//             onClick={startQuiz}
//             className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium"
//           >
//             Start Quiz
//           </button>
//         </div>
//       )}

//       {error && (
//         <div className="text-center bg-red-50 border border-red-200 rounded-lg p-4">
//           <p className="text-red-600">{error}</p>
//         </div>
//       )}

//       {questionData && (
//         <div className="text-gray-800">
//           <div className="bg-gray-50 rounded-lg p-4 mb-4 border border-gray-200">
//             <div className="mb-2">
//               <strong>Quiz ID:</strong> <span className="text-blue-600">{quizId}</span>
//             </div>
//             <div>
//               <strong>Status:</strong> <span className="text-green-600">{quizStatus}</span>
//             </div>
//           </div>

//           <div className="flex gap-6 mb-6">
//             <div className="flex-1">
//               <video
//                 ref={videoRef}
//                 autoPlay
//                 playsInline
//                 muted
//                 className="w-full max-w-xl rounded-lg border border-gray-300 shadow-sm"
//               />
//               <canvas ref={canvasRef} className="hidden" />
//             </div>

//             <div className="w-80 space-y-4">
//               {/* Live Feed Logs */}
//               <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 max-h-96 overflow-y-auto">
//                 <h3 className="text-lg font-semibold mb-3 text-gray-800">
//                   Live Hand Raises ({liveFeedLogs.length})
//                 </h3>
//                 {liveFeedLogs.length === 0 ? (
//                   <p className="text-gray-500 text-sm">No hand raises detected yet...</p>
//                 ) : (
//                   <div className="space-y-2">
//                     {liveFeedLogs
//                       .sort(
//                         (a, b) =>
//                           new Date(b.timestamp || b.detection_timestamp) -
//                           new Date(a.timestamp || a.detection_timestamp)
//                       )
//                       .map((log, idx) => (
//                         <div
//                           key={log.id || idx}
//                           className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm"
//                         >
//                           <div className="flex justify-between items-start mb-1">
//                             <span className="font-medium text-gray-800">
//                               #{liveFeedLogs.length - idx} {log.student_name}
//                             </span>
//                             <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
//                               {log.responseTime}s
//                             </span>
//                           </div>
//                           <div className="flex justify-between items-center">
//                             <span className="text-blue-600 font-medium">
//                               Option {log.option?.toUpperCase()}
//                             </span>
//                             <span className="text-xs text-gray-400">
//                               {new Date(
//                                 log.timestamp || log.detection_timestamp
//                               ).toLocaleTimeString()}
//                             </span>
//                           </div>
//                           {log.confidence && (
//                             <div className="text-xs text-gray-500 mt-1">
//                               Confidence: {(log.confidence * 100).toFixed(1)}%
//                             </div>
//                           )}
//                         </div>
//                       ))}
//                   </div>
//                 )}
//               </div>

//               {/* Debug Logs */}
//               <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 max-h-64 overflow-y-auto">
//                 <h3 className="text-sm font-semibold mb-2 text-gray-800">
//                   Debug Logs ({debugLogs.length})
//                 </h3>
//                 <div className="space-y-1">
//                   {debugLogs.slice(-10).map((log, idx) => (
//                     <div key={idx} className="text-xs">
//                       <span className="text-gray-400">
//                         {new Date(log.timestamp).toLocaleTimeString()}
//                       </span>
//                       <span className="ml-2 text-gray-700">{log.message}</span>
//                     </div>
//                   ))}
//                 </div>
//               </div>

//               {/* Socket Events */}
//               <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 max-h-64 overflow-y-auto">
//                 <h3 className="text-sm font-semibold mb-2 text-gray-800">
//                   Socket Events ({socketEvents.length})
//                 </h3>
//                 <div className="space-y-1">
//                   {socketEvents.slice(-10).map((event, idx) => (
//                     <div key={idx} className="text-xs">
//                       <span className="text-gray-400">
//                         {new Date(event.timestamp).toLocaleTimeString()}
//                       </span>
//                       <span className="ml-2 text-purple-600 font-medium">{event.event}</span>
//                     </div>
//                   ))}
//                 </div>
//               </div>
//             </div>
//           </div>

//           <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
//             <h2 className="text-xl font-semibold mb-3 text-gray-800">
//               {questionData.sub_topic_name}
//             </h2>

//             <ReadingTimer timeRemaining={questionTimer} isActive={isReadingQuestion} />

//             <p className="text-lg mb-4 text-gray-700">
//               {questionData.question_number}. {questionData.question}
//             </p>

//             <div className="grid gap-3 mb-4">
//               {questionData.options.map((opt, idx) => {
//                 const optionLetter = String.fromCharCode(97 + idx);
//                 const isCurrentOption = currentOption.toLowerCase() === optionLetter;

//                 return (
//                   <div key={idx} className="relative">
//                     <div
//                       className={`p-3 rounded-lg border transition-all ${
//                         isCurrentOption
//                           ? 'bg-green-100 border-green-300 text-green-800'
//                           : 'bg-gray-50 border-gray-200 text-gray-700'
//                       }`}
//                     >
//                       <span className="font-medium">{optionLetter.toUpperCase()})</span> {opt}
//                     </div>
//                     <CircularOptionDisplay
//                       option={currentOption}
//                       timeRemaining={currentOptionTimer}
//                       totalTime={DURATION_PER_OPTION_S}
//                       isActive={isCurrentOption && currentOptionTimer !== null}
//                     />
//                   </div>
//                 );
//               })}
//             </div>
//           </div>

//           <button
//             onClick={handleNextQuestion}
//             className="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium"
//             disabled={
//               quizStatus.includes('Capturing') ||
//               quizStatus.includes('Processing') ||
//               isReadingQuestion
//             }
//           >
//             Start Capture & Go to Next Question
//           </button>
//         </div>
//       )}

//       {finalResults && questionCount < 5 && (
//         <div className="bg-gray-50 rounded-lg p-6 mt-6 border border-gray-200">
//           <h3 className="text-lg font-semibold mb-4 text-gray-800">Quiz Finished!</h3>

//           <h4 className="text-md font-semibold mb-2 text-gray-700">Final Raw Data:</h4>
//           <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-auto max-h-64 text-sm">
//             {JSON.stringify(finalResults, null, 2)}
//           </pre>
//         </div>
//       )}
//     </div>
//   );
// };

// export default ContentQuiz;

import React, { useEffect, useRef, useState } from 'react';
import io from 'socket.io-client';
import { X, BarChart3, Clock } from 'lucide-react';
import EngagementDashboard from './EngagementDashboard';

const FLASK_API_URL = 'https://sasthra.in';
const SOCKETIO_URL = 'https://sasthra.in';
const CAPTURE_INTERVAL_MS = 200;
const DURATION_PER_OPTION_S = 5;
const QUESTION_READING_TIME_S = 15;

const ReadingTimer = ({ timeRemaining, isActive }) => {
  if (!isActive) return null;

  return (
    <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-40">
      <div className="bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 text-white px-12 py-8 rounded-3xl shadow-2xl animate-pulse border-4 border-white/30 backdrop-blur-sm">
        <div className="text-center">
          <div className="text-3xl font-bold mb-3 flex items-center justify-center gap-3">
            <Clock className="animate-spin" size={32} />
            Reading Time
          </div>
          <div className="text-6xl font-black animate-bounce drop-shadow-lg">{timeRemaining}s</div>
          <div className="mt-3 text-lg opacity-90">Prepare for the question</div>
        </div>
      </div>
    </div>
  );
};

const CircularOptionDisplay = ({ option, timeRemaining, totalTime, isActive }) => {
  const circumference = 2 * Math.PI * 120; // radius = 120 for larger circle
  const progress = isActive ? (timeRemaining / totalTime) * circumference : 0;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - progress;

  // Color animation based on time remaining
  const getTimerColor = () => {
    const percentage = timeRemaining / totalTime;
    if (percentage > 0.6) return '#10B981'; // Green
    if (percentage > 0.3) return '#F59E0B'; // Yellow
    return '#EF4444'; // Red
  };

  // Scale animation based on time
  const getScale = () => {
    const percentage = timeRemaining / totalTime;
    return 1 + (1 - percentage) * 0.2; // Grows as time decreases
  };

  if (!isActive) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
      {/* Animated background with multiple layers */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/80 via-purple-50/80 to-pink-50/80 backdrop-blur-sm animate-pulse" />

      {/* Floating background elements */}
      <div className="absolute inset-0">
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="absolute w-4 h-4 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 animate-float"
            style={{
              top: `${10 + i * 7}%`,
              left: `${5 + i * 8}%`,
              animationDelay: `${i * 0.3}s`,
              animationDuration: `${3 + (i % 3)}s`
            }}
          />
        ))}
      </div>

      {/* Main circular container with enhanced animations */}
      <div
        className="relative transition-all duration-300 ease-out"
        style={{ transform: `scale(${getScale()})` }}
      >
        {/* Outer glow ring */}
        <div className="absolute inset-0 w-80 h-80 rounded-full bg-gradient-to-r from-blue-200 via-purple-200 to-pink-200 opacity-30 animate-spin-slow blur-xl" />

        {/* Main circle container */}
        <div className="relative w-64 h-64 flex items-center justify-center">
          {/* Background circle with gradient */}
          <svg
            className="absolute inset-0 transform -rotate-90 w-full h-full drop-shadow-2xl"
            viewBox="0 0 260 260"
          >
            {/* Background track */}
            <circle
              cx="130"
              cy="130"
              r="120"
              fill="none"
              stroke="rgba(255, 255, 255, 0.3)"
              strokeWidth="12"
              className="drop-shadow-lg"
            />

            {/* Animated progress circle */}
            <circle
              cx="130"
              cy="130"
              r="120"
              fill="none"
              stroke={getTimerColor()}
              strokeWidth="12"
              strokeLinecap="round"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              className="transition-all duration-1000 ease-out drop-shadow-xl"
              style={{
                filter: `drop-shadow(0 0 20px ${getTimerColor()}40)`
              }}
            />

            {/* Inner decorative circles */}
            <circle
              cx="130"
              cy="130"
              r="100"
              fill="none"
              stroke="rgba(255, 255, 255, 0.2)"
              strokeWidth="2"
              strokeDasharray="5,5"
              className="animate-spin-reverse"
            />

            {/* Gradient definitions */}
            <defs>
              <linearGradient id="optionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#3B82F6" />
                <stop offset="50%" stopColor="#8B5CF6" />
                <stop offset="100%" stopColor="#EC4899" />
              </linearGradient>
              <radialGradient id="centerGradient" cx="50%" cy="50%" r="50%">
                <stop offset="0%" stopColor="#FFFFFF" />
                <stop offset="100%" stopColor="#F8FAFC" />
              </radialGradient>
            </defs>
          </svg>

          {/* Center content with enhanced styling */}
          <div className="relative z-10 flex flex-col items-center justify-center bg-gradient-to-br from-white via-blue-50 to-purple-50 rounded-full w-48 h-48 shadow-2xl border-4 border-white/50 backdrop-blur-sm">
            {/* Option letter with enhanced animation */}
            <div
              className="text-8xl font-black bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-2 transform transition-all duration-500"
              style={{
                transform: `scale(${1 + (1 - timeRemaining / totalTime) * 0.3}) rotate(${(totalTime - timeRemaining) * 2}deg)`,
                textShadow: '0 0 30px rgba(59, 130, 246, 0.3)'
              }}
            >
              {option}
            </div>

            {/* Animated countdown number */}
            <div
              className="text-4xl font-bold bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent animate-pulse"
              style={{
                transform: `scale(${1 + (1 - timeRemaining / totalTime) * 0.2})`,
                filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
              }}
            >
              {timeRemaining}s
            </div>

            {/* Progress indicator dots */}
            <div className="flex gap-1 mt-2">
              {[...Array(totalTime)].map((_, i) => (
                <div
                  key={i}
                  className={`w-1.5 h-1.5 rounded-full transition-all duration-300 ${
                    i < totalTime - timeRemaining
                      ? 'bg-gradient-to-r from-red-400 to-orange-400 scale-125'
                      : 'bg-gray-300 scale-100'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Floating particles with enhanced animation */}
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(8)].map((_, i) => (
              <div
                key={i}
                className="absolute w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-bounce opacity-60"
                style={{
                  top: `${15 + i * 10}%`,
                  left: `${10 + i * 12}%`,
                  animationDelay: `${i * 0.3}s`,
                  animationDuration: `${2 + (i % 2)}s`
                }}
              />
            ))}
          </div>

          {/* Rotating outer ring */}
          <div className="absolute inset-0 w-full h-full border-2 border-gradient-to-r from-blue-300 to-purple-300 rounded-full animate-spin opacity-20" />
        </div>
      </div>
    </div>
  );
};

const Loader = () => (
  <div className="flex items-center justify-center h-64">
    <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500 border-solid"></div>
    <span className="ml-4 text-lg text-gray-600">Loading question...</span>
  </div>
);

const ContentQuiz = ({ processId, onClose }) => {
  const [quizId, setQuizId] = useState(null);
  const [questionData, setQuestionData] = useState(null);
  const [quizStatus, setQuizStatus] = useState('');
  const [summary, setSummary] = useState(null);
  const [finalResults, setFinalResults] = useState(null);
  const [liveFeedLogs, setLiveFeedLogs] = useState([]);
  const [currentQuestionStartTime, setCurrentQuestionStartTime] = useState(null);
  const [socketStatus, setSocketStatus] = useState('Disconnected');
  const [error, setError] = useState(null);
  const [centerCode] = useState(sessionStorage.getItem('centercode') || '');
  const [currentOptionTimer, setCurrentOptionTimer] = useState(null);
  const [currentOption, setCurrentOption] = useState('');
  const [questionTimer, setQuestionTimer] = useState(null);
  const [isReadingQuestion, setIsReadingQuestion] = useState(false);
  const [showDashboard, setShowDashboard] = useState(false);
  const [questionCount, setQuestionCount] = useState(0);
  const [debugLogs, setDebugLogs] = useState([]);
  const [socketEvents, setSocketEvents] = useState([]);
  const [isCapturing, setIsCapturing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const socketRef = useRef(null);
  const videoStreamRef = useRef(null);
  const timerIntervalRef = useRef(null);
  const optionTimerIntervalRef = useRef(null);

  // Debug logging function
  const addDebugLog = (message, data = null) => {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      message,
      data: data ? JSON.stringify(data) : null
    };
    console.log(`[DEBUG ${timestamp}] ${message}`, data);
    setDebugLogs((prev) => [...prev.slice(-50), logEntry]); // Keep last 50 logs
  };

  // Socket event logging
  const addSocketEvent = (eventName, data = null) => {
    const timestamp = new Date().toISOString();
    const eventEntry = {
      timestamp,
      event: eventName,
      data: data ? JSON.stringify(data) : null
    };
    console.log(`[SOCKET ${timestamp}] ${eventName}`, data);
    setSocketEvents((prev) => [...prev.slice(-50), eventEntry]); // Keep last 50 events
  };

  useEffect(() => {
    if (quizId && videoRef.current) {
      initCamera();
    }
  }, [quizId]);

  useEffect(() => {
    return () => {
      addDebugLog('Component unmounting, cleaning up...');
      stopCamera();
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
      }
      if (optionTimerIntervalRef.current) {
        clearInterval(optionTimerIntervalRef.current);
      }
    };
  }, []);

  const startQuiz = async () => {
    addDebugLog('Starting quiz...', { processId, centerCode });

    if (!processId || !centerCode) {
      setError('Missing ProcessId or CenterCode');
      return;
    }

    setIsLoading(true);
    try {
      const res = await fetch(`${FLASK_API_URL}/content/start_quiz`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ process_selector_id: processId, center_code: centerCode })
      });

      const data = await res.json();
      addDebugLog('Quiz start response received', data);

      if (!res.ok || data.error) throw new Error(data.error || `Server error ${res.status}`);

      setQuestionData(data);
      setQuizId(data.quiz_id);
      setQuizStatus('Ready to start capture for this question.');
      setQuestionCount(1);
      addDebugLog('Quiz started successfully', { quizId: data.quiz_id });
      setupSocketIO();
    } catch (error) {
      addDebugLog('Error starting quiz', error);
      setError(`Error starting quiz: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNextQuestion = async () => {
    if (!quizId) return;

    addDebugLog('Handling next question...');

    try {
      await startCaptureCycle();
      setQuizStatus('Processing results and fetching next question...');

      const res = await fetch(`${FLASK_API_URL}/content/next_question`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ quiz_id: quizId, process_selector_id: processId })
      });

      const data = await res.json();
      addDebugLog('Next question response', data);

      if (data.overall_summary) {
        setFinalResults(data);
        stopCamera();
        socketRef.current?.disconnect();
        setQuizStatus('Quiz completed! View engagement dashboard for detailed analytics.');
        addDebugLog('Quiz completed', { questionCount });
        if (questionCount >= 5) {
          setShowDashboard(true);
        }
      } else if (data.question) {
        setQuestionData(data);
        setQuizStatus('Ready to start capture for this question.');
        setQuestionCount((prev) => prev + 1);
        addDebugLog('Next question loaded', { questionCount: questionCount + 1 });
      }
    } catch (error) {
      addDebugLog('Error during quiz progression', error);
      setError(`Error during quiz progression: ${error.message}`);
      setQuizStatus(`Error: ${error.message}`);
    }
  };

  const startCaptureCycle = async () => {
    addDebugLog('Starting capture cycle...');
    setLiveFeedLogs([]);
    setIsCapturing(true);

    // Question reading phase
    addDebugLog('Starting question reading phase');
    setIsReadingQuestion(true);
    setQuestionTimer(QUESTION_READING_TIME_S);
    setCurrentQuestionStartTime(Date.now());

    timerIntervalRef.current = setInterval(() => {
      setQuestionTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timerIntervalRef.current);
          setIsReadingQuestion(false);
          addDebugLog('Question reading phase completed');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    await new Promise((resolve) => setTimeout(resolve, QUESTION_READING_TIME_S * 1000));

    setIsReadingQuestion(false);
    setQuestionTimer(null);

    // Options capture phase
    addDebugLog('Starting options capture phase');
    const options = ['a', 'b', 'c', 'd'];

    for (let i = 0; i < options.length; i++) {
      addDebugLog(`Processing option ${options[i]}`, { optionIndex: i });
      setQuizStatus(
        `Capturing for option ${options[i].toUpperCase()}... (${i + 1}/${options.length})`
      );
      await processSingleOption(options[i]);
    }

    setQuizStatus('Capture complete for this question. Results sent.');
    addDebugLog('Capture cycle completed');

    setCurrentOptionTimer(null);
    setCurrentOption('');
    setIsCapturing(false);
  };

  const processSingleOption = (optionChar) => {
    addDebugLog(`Processing option ${optionChar}...`);

    return new Promise((resolve) => {
      const startTime = Date.now();
      let frameCount = 0;

      setCurrentOption(optionChar.toUpperCase());
      setCurrentOptionTimer(DURATION_PER_OPTION_S);

      // Start option timer
      optionTimerIntervalRef.current = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        const remaining = Math.max(0, DURATION_PER_OPTION_S - elapsed);
        setCurrentOptionTimer(remaining);

        if (remaining <= 0) {
          clearInterval(optionTimerIntervalRef.current);
        }
      }, 1000);

      // Start frame capture
      const intervalId = setInterval(() => {
        const responseTime = (Date.now() - startTime) / 1000;
        captureAndSendFrame(optionChar, responseTime);
        frameCount++;
      }, CAPTURE_INTERVAL_MS);

      // Stop after duration
      setTimeout(() => {
        clearInterval(intervalId);
        if (optionTimerIntervalRef.current) {
          clearInterval(optionTimerIntervalRef.current);
        }
        addDebugLog(`Option ${optionChar} processing completed`, { frameCount });
        resolve();
      }, DURATION_PER_OPTION_S * 1000);
    });
  };

  const captureAndSendFrame = (optionChar, responseTime) => {
    const video = videoRef.current;
    const canvas = canvasRef.current;

    if (!video || !canvas) {
      addDebugLog('Video or canvas not available for frame capture');
      return;
    }

    if (!socketRef.current?.connected) {
      addDebugLog('Socket not connected, skipping frame capture');
      return;
    }

    const ctx = canvas.getContext('2d');
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    const frameData = canvas.toDataURL('image/jpeg', 0.6);

    const framePayload = {
      quiz_id: quizId,
      frame: frameData,
      option_char: optionChar,
      response_time_seconds: responseTime
    };

    addDebugLog(`Sending frame for option ${optionChar}`, {
      optionChar,
      responseTime,
      frameSize: frameData.length
    });

    socketRef.current.emit('process_frame', framePayload);
  };

  const setupSocketIO = () => {
    addDebugLog('Setting up Socket.IO connection...');

    if (socketRef.current) {
      socketRef.current.disconnect();
    }

    socketRef.current = io(SOCKETIO_URL, {
      path: '/socketio1/socket.io',
      transports: ['websocket', 'polling'],
      upgrade: true,
      rememberUpgrade: true,
      timeout: 20000,
      forceNew: true,
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      maxReconnectionAttempts: 5
    });

    socketRef.current.on('connect', () => {
      setSocketStatus('Connected');
      addDebugLog('Socket connected successfully');
      addSocketEvent('connect');
    });

    socketRef.current.on('disconnect', (reason) => {
      setSocketStatus('Disconnected');
      addDebugLog('Socket disconnected', { reason });
      addSocketEvent('disconnect', { reason });
    });

    socketRef.current.on('connect_error', (error) => {
      setSocketStatus('Connection Error');
      addDebugLog('Socket connection error', error);
      addSocketEvent('connect_error', error);
    });

    socketRef.current.on('reconnect', (attemptNumber) => {
      setSocketStatus('Reconnected');
      addDebugLog('Socket reconnected', { attemptNumber });
      addSocketEvent('reconnect', { attemptNumber });
    });

    socketRef.current.on('reconnect_error', (error) => {
      addDebugLog('Socket reconnection error', error);
      addSocketEvent('reconnect_error', error);
    });

    socketRef.current.on('reconnect_failed', () => {
      setSocketStatus('Reconnection Failed');
      addDebugLog('Socket reconnection failed');
      addSocketEvent('reconnect_failed');
    });

    // Main hand raise event handler
    socketRef.current.on('hand_raised', (data) => {
      addDebugLog('Hand raised event received', data);
      addSocketEvent('hand_raised', data);

      const timestamp = new Date(data.detection_timestamp || Date.now());
      const responseTime = currentQuestionStartTime
        ? Math.round((timestamp.getTime() - currentQuestionStartTime) / 1000)
        : 0;

      const newLog = {
        id: Date.now() + Math.random(), // Unique ID
        student_name: data.student_name || 'Unknown Student',
        option: data.option || currentOption || 'Unknown',
        responseTime: responseTime,
        timestamp: timestamp.toISOString(),
        detection_timestamp: data.detection_timestamp,
        confidence: data.confidence || 0,
        raw_data: data
      };

      addDebugLog('Processing hand raise', newLog);

      setLiveFeedLogs((prevLogs) => {
        const updatedLogs = [...prevLogs, newLog];
        addDebugLog('Live feed logs updated', { count: updatedLogs.length });
        return updatedLogs;
      });
    });

    // Listen for all possible hand detection events
    const handEvents = [
      'hand_raised',
      'hand_detected',
      'gesture_detected',
      'student_response',
      'frame_processed',
      'detection_result'
    ];

    handEvents.forEach((eventName) => {
      socketRef.current.on(eventName, (data) => {
        addDebugLog(`Event received: ${eventName}`, data);
        addSocketEvent(eventName, data);

        // If it's a hand-related event, try to process it
        if (
          data &&
          (data.hand_raised || data.gesture === 'hand_raised' || eventName.includes('hand'))
        ) {
          const timestamp = new Date(data.detection_timestamp || Date.now());
          const responseTime = currentQuestionStartTime
            ? Math.round((timestamp.getTime() - currentQuestionStartTime) / 1000)
            : 0;

          const newLog = {
            id: Date.now() + Math.random(),
            student_name: data.student_name || data.name || 'Student',
            option: data.option || currentOption || 'Unknown',
            responseTime: responseTime,
            timestamp: timestamp.toISOString(),
            detection_timestamp: data.detection_timestamp,
            confidence: data.confidence || 0,
            event_type: eventName,
            raw_data: data
          };

          setLiveFeedLogs((prevLogs) => [...prevLogs, newLog]);
        }
      });
    });

    // Catch all events
    socketRef.current.onAny((eventName, ...args) => {
      addDebugLog(`Socket event: ${eventName}`, args);
      addSocketEvent(eventName, args);
    });

    addDebugLog('Socket.IO setup completed');
  };

  const initCamera = async () => {
    if (videoStreamRef.current) return;

    addDebugLog('Initializing camera...');

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoStreamRef.current = stream;
        addDebugLog('Camera initialized successfully');
      }
    } catch (error) {
      addDebugLog('Camera initialization error', error);
      setError(`Camera error: ${error.message}`);
    }
  };

  const stopCamera = () => {
    addDebugLog('Stopping camera...');
    if (videoStreamRef.current) {
      videoStreamRef.current.getTracks().forEach((track) => track.stop());
      videoStreamRef.current = null;
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
  };

  const testSocketConnection = () => {
    if (socketRef.current) {
      addDebugLog('Testing socket connection...');
      socketRef.current.emit('test_connection', {
        message: 'Hello from client',
        timestamp: Date.now()
      });
    }
  };

  const clearDebugLogs = () => {
    setDebugLogs([]);
    setSocketEvents([]);
    addDebugLog('Debug logs cleared');
  };

  const handleCloseDashboard = () => {
    setShowDashboard(false);
  };

  useEffect(() => {
    if (processId && centerCode) {
      startQuiz();
    }
  }, [processId, centerCode]);

  if (showDashboard && quizId) {
    return <EngagementDashboard quizId={quizId} onClose={handleCloseDashboard} />;
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-8 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">AI Tutor Quiz Tester</h2>
        <button onClick={onClose} className="text-gray-600 hover:text-red-500 transition-colors">
          <X size={24} />
        </button>
      </div>

      {isLoading && <Loader />}

      <div
        className={`p-4 rounded-lg mb-6 border ${
          socketStatus === 'Connected'
            ? 'bg-green-50 border-green-200 text-green-800'
            : 'bg-red-50 border-red-200 text-red-800'
        }`}
      >
        <div className="flex justify-between items-center">
          <div>
            <strong>Socket Status:</strong> {socketStatus}
            <span className="ml-4 text-sm">Capturing: {isCapturing ? 'Yes' : 'No'}</span>
          </div>
          <div className="flex gap-2">
            {socketRef.current && (
              <button
                onClick={testSocketConnection}
                className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors text-sm"
              >
                Test Connection
              </button>
            )}
            <button
              onClick={clearDebugLogs}
              className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors text-sm"
            >
              Clear Logs
            </button>
          </div>
        </div>
      </div>

      {!questionData && !error && !isLoading && (
        <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
          <h2 className="text-lg font-semibold mb-4 text-gray-800">Start a New Quiz</h2>
          <div className="form-group mb-4">
            <label htmlFor="process-selector-id" className="block mb-2 text-gray-700 font-medium">
              Process Selector ID:
            </label>
            <input
              type="text"
              id="process-selector-id"
              value={processId || ''}
              readOnly
              className="w-full p-3 border border-gray-300 rounded-lg bg-gray-100 text-gray-700"
            />
          </div>
          <div className="form-group mb-4">
            <label htmlFor="center-code" className="block mb-2 text-gray-700 font-medium">
              Center Code:
            </label>
            <input
              type="text"
              id="center-code"
              value={centerCode}
              readOnly
              className="w-full p-3 border border-gray-300 rounded-lg bg-gray-100 text-gray-700"
            />
          </div>
          <button
            onClick={startQuiz}
            className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium"
          >
            Start Quiz
          </button>
        </div>
      )}

      {error && (
        <div className="text-center bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {questionData && !isLoading && (
        <div className="text-gray-800">
          <div className="bg-gray-50 rounded-lg p-4 mb-4 border border-gray-200">
            <div className="mb-2">
              <strong>Quiz ID:</strong> <span className="text-blue-600">{quizId}</span>
            </div>
            <div>
              <strong>Status:</strong> <span className="text-green-600">{quizStatus}</span>
            </div>
          </div>

          <div className="flex gap-6 mb-6">
            <div className="flex-1">
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                className="w-full max-w-xl rounded-lg border border-gray-300 shadow-sm"
              />
              <canvas ref={canvasRef} className="hidden" />
            </div>

            <div className="w-80 space-y-4">
              {/* Live Feed Logs */}
              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 max-h-96 overflow-y-auto">
                <h3 className="text-lg font-semibold mb-3 text-gray-800">
                  Live Hand Raises ({liveFeedLogs.length})
                </h3>
                {liveFeedLogs.length === 0 ? (
                  <p className="text-gray-500 text-sm">No hand raises detected yet...</p>
                ) : (
                  <div className="space-y-2">
                    {liveFeedLogs
                      .sort(
                        (a, b) =>
                          new Date(b.timestamp || b.detection_timestamp) -
                          new Date(a.timestamp || a.detection_timestamp)
                      )
                      .map((log, idx) => (
                        <div
                          key={log.id || idx}
                          className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm"
                        >
                          <div className="flex justify-between items-start mb-1">
                            <span className="font-medium text-gray-800">
                              #{liveFeedLogs.length - idx} {log.student_name}
                            </span>
                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                              {log.responseTime}s
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-blue-600 font-medium">
                              Option {log.option?.toUpperCase()}
                            </span>
                            <span className="text-xs text-gray-400">
                              {new Date(
                                log.timestamp || log.detection_timestamp
                              ).toLocaleTimeString()}
                            </span>
                          </div>
                          {log.confidence && (
                            <div className="text-xs text-gray-500 mt-1">
                              Confidence: {(log.confidence * 100).toFixed(1)}%
                            </div>
                          )}
                        </div>
                      ))}
                  </div>
                )}
              </div>

              {/* Debug Logs */}
              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 max-h-64 overflow-y-auto">
                <h3 className="text-sm font-semibold mb-2 text-gray-800">
                  Debug Logs ({debugLogs.length})
                </h3>
                <div className="space-y-1">
                  {debugLogs.slice(-10).map((log, idx) => (
                    <div key={idx} className="text-xs">
                      <span className="text-gray-400">
                        {new Date(log.timestamp).toLocaleTimeString()}
                      </span>
                      <span className="ml-2 text-gray-700">{log.message}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Socket Events */}
              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 max-h-64 overflow-y-auto">
                <h3 className="text-sm font-semibold mb-2 text-gray-800">
                  Socket Events ({socketEvents.length})
                </h3>
                <div className="space-y-1">
                  {socketEvents.slice(-10).map((event, idx) => (
                    <div key={idx} className="text-xs">
                      <span className="text-gray-400">
                        {new Date(event.timestamp).toLocaleTimeString()}
                      </span>
                      <span className="ml-2 text-purple-600 font-medium">{event.event}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold mb-3 text-gray-800">
              {questionData.sub_topic_name}
            </h2>

            <ReadingTimer timeRemaining={questionTimer} isActive={isReadingQuestion} />

            <p className="text-lg mb-4 text-gray-700">
              {questionData.question_number}. {questionData.question}
            </p>

            <div className="grid gap-3 mb-4">
              {questionData.options.map((opt, idx) => {
                const optionLetter = String.fromCharCode(97 + idx);
                const isCurrentOption = currentOption.toLowerCase() === optionLetter;

                return (
                  <div key={idx} className="relative">
                    <div
                      className={`p-3 rounded-lg border transition-all ${
                        isCurrentOption
                          ? 'bg-green-100 border-green-300 text-green-800'
                          : 'bg-gray-50 border-gray-200 text-gray-700'
                      }`}
                    >
                      <span className="font-medium">{optionLetter.toUpperCase()})</span> {opt}
                    </div>
                    <CircularOptionDisplay
                      option={currentOption}
                      timeRemaining={currentOptionTimer}
                      totalTime={DURATION_PER_OPTION_S}
                      isActive={isCurrentOption && currentOptionTimer !== null}
                    />
                  </div>
                );
              })}
            </div>
          </div>

          <button
            onClick={handleNextQuestion}
            className="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium"
            disabled={
              quizStatus.includes('Capturing') ||
              quizStatus.includes('Processing') ||
              isReadingQuestion
            }
          >
            Start Capture & Go to Next Question
          </button>
        </div>
      )}

      {finalResults && questionCount < 5 && (
        <div className="bg-gray-50 rounded-lg p-6 mt-6 border border-gray-200">
          <h3 className="text-lg font-semibold mb-4 text-gray-800">Quiz Finished!</h3>

          <h4 className="text-md font-semibold mb-2 text-gray-700">Final Raw Data:</h4>
          <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-auto max-h-64 text-sm">
            {JSON.stringify(finalResults, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default ContentQuiz;
